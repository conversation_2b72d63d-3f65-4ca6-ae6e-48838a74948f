#!/usr/bin/env python3
"""
Test script to verify Explorer path extraction functionality.
This script will detect Explorer windows and show their extracted paths.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from window_tiler import WindowManager, Config

def test_explorer_path_extraction():
    """Test the enhanced Explorer path extraction."""
    print("Testing Explorer Path Extraction")
    print("=" * 50)
    
    # Create window manager
    config = Config()
    window_manager = WindowManager(config)
    
    # Detect windows
    print("Detecting windows...")
    window_manager.detect_windows()
    
    # Filter for Explorer windows
    explorer_windows = [w for w in window_manager.windows if w.process_name.lower() == 'explorer.exe']
    
    if not explorer_windows:
        print("❌ No Explorer windows found!")
        print("💡 Please open some File Explorer windows and try again.")
        return
    
    print(f"✅ Found {len(explorer_windows)} Explorer window(s):")
    print()
    
    for i, window in enumerate(explorer_windows, 1):
        print(f"Window {i}:")
        print(f"  HWND: {window.hwnd}")
        print(f"  Original Title: '{window.title}'")
        print(f"  Process: {window.process_name}")
        print(f"  Type: {window.type}")
        
        # Show detailed info
        if hasattr(window, 'detailed_info'):
            info = window.detailed_info
            print(f"  Extracted Path: {info.get('path', 'None')}")
            print(f"  Display Name: '{info.get('display_name', 'None')}'")
        else:
            print("  ❌ No detailed info available")
        
        print()

if __name__ == "__main__":
    test_explorer_path_extraction()
