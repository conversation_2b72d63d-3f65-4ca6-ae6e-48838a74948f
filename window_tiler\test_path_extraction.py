#!/usr/bin/env python3
"""
Test script to verify enhanced path extraction functionality.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from window_organizer import export_windows_for_editing

class MockWindow:
    """Mock Window class with enhanced path extraction."""
    def __init__(self, title, process_name, window_type="Unknown"):
        self.title = title
        self.process_name = process_name
        self.type = window_type
        self.hwnd = 12345
        self.detailed_info = self._extract_detailed_info()
    
    def _extract_detailed_info(self):
        """Extract detailed information about the window."""
        info = {
            'title': self.title,
            'path': None,
            'display_name': self.title
        }
        
        if self.process_name.lower() == 'sublime_text.exe':
            path = self._extract_sublime_path()
            if path:
                info['path'] = path
                info['display_name'] = path
        
        return info
    
    def _extract_sublime_path(self):
        """Extract file path from Sublime Text window title."""
        title = self.title.strip()
        
        if ' - Sublime Text' in title:
            content = title.replace(' - Sublime Text', '').strip()
            
            # Look for path patterns with drive letters
            import re
            path_match = re.search(r'[A-Za-z]:\\[^|•-]*', content)
            if path_match:
                return path_match.group(0).strip()
            
            # Look for patterns like "filename - C:\path"
            if ' - ' in content:
                parts = content.split(' - ')
                for part in reversed(parts):
                    if ':' in part and ('\\' in part or '/' in part):
                        return part.strip()
            
            # Look for patterns with bullet separator
            if ' • ' in content:
                parts = content.split(' • ')
                for part in parts:
                    if ':' in part and ('\\' in part or '/' in part):
                        return part.strip()
        
        return None

def test_enhanced_path_extraction():
    """Test the enhanced path extraction with realistic Sublime Text titles."""
    print("Testing Enhanced Path Extraction")
    print("=" * 50)
    
    # Create mock windows with realistic Sublime Text titles
    test_windows = [
        MockWindow("Prompting - C:\\Users\\<USER>\\Desktop\\window_organization.md - Sublime Text", 
                  "sublime_text.exe", "Editor"),
        MockWindow("py__WindowTiler - C:\\Users\\<USER>\\Desktop\\my\\flow\\home\\__GOTO__\\Scripts\\Python\\Py_Cli_Utils\\py__WindowTiler - Sublime Text", 
                  "sublime_text.exe", "Editor"),
        MockWindow("C:\\Users\\<USER>\\Desktop\\my\\flow\\home\\__GOTO__\\Notes\\system_info.md - Sublime Text", 
                  "sublime_text.exe", "Editor"),
        MockWindow("ai_system_0013_wip - C:\\Users\\<USER>\\Desktop\\my\\flow\\home\\__GOTO__\\Notes - Sublime Text", 
                  "sublime_text.exe", "Editor"),
        MockWindow("__meta__ - fe wfew ef • - Sublime Text", 
                  "sublime_text.exe", "Editor"),
    ]
    
    print("Mock windows created:")
    for i, window in enumerate(test_windows, 1):
        print(f"{i}. Original: {window.title}")
        print(f"   Extracted Path: {window.detailed_info.get('path', 'None')}")
        print()
    
    # Export to markdown
    print("Exporting to enhanced markdown table...")
    file_path = export_windows_for_editing(test_windows, "Sublime Text Windows")
    print(f"✅ Exported to: {file_path}")
    
    # Show the generated content
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("\nGenerated markdown content:")
    print("-" * 80)
    print(content)
    
    # Clean up
    os.remove(file_path)
    print("✅ Cleaned up temporary file")

if __name__ == "__main__":
    test_enhanced_path_extraction()
