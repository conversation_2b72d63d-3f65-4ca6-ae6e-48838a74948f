#!/usr/bin/env python3
"""
Test script to verify enhanced path extraction functionality.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from window_organizer import export_windows_for_editing

class MockWindow:
    """Mock Window class with enhanced path extraction."""
    def __init__(self, title, process_name, window_type="Unknown"):
        self.title = title
        self.process_name = process_name
        self.type = window_type
        self.hwnd = 12345
        self.detailed_info = self._extract_detailed_info()
    
    def _extract_detailed_info(self):
        """Extract detailed information about the window."""
        info = {
            'title': self.title,
            'path': None,
            'display_name': self.title
        }

        # Use generalized path extraction for all applications
        path = self._extract_generic_path()
        if path:
            info['path'] = path
            info['display_name'] = path

        return info

    def _extract_generic_path(self):
        """Extract file/folder paths from window titles using generalized patterns."""
        title = self.title.strip()
        import re

        # Define common application suffixes to remove
        app_suffixes = [
            ' - Sublime Text',
            ' - Visual Studio Code',
            ' - Notepad',
            ' - File Explorer',
            ' • Sublime Text'
        ]

        # Remove application suffix if present
        content = title
        for suffix in app_suffixes:
            if suffix in content:
                content = content.replace(suffix, '').strip()
                break

        # Strategy 1: Look for complete Windows paths
        path_patterns = [
            r'[A-Za-z]:\\[^|•\n\r]*',  # Full Windows path
        ]

        for pattern in path_patterns:
            matches = re.findall(pattern, content)
            if matches:
                longest_match = max(matches, key=len).strip()
                longest_match = longest_match.rstrip(' -•')
                if len(longest_match) > 3:
                    return longest_match

        # Strategy 2: Parse structured title formats
        separators = [' - ', ' • ', ' | ']

        for separator in separators:
            if separator in content:
                parts = content.split(separator)
                for part in reversed(parts):
                    part = part.strip()
                    if ':' in part and ('\\' in part or '/' in part):
                        part = part.rstrip(' -•')
                        if len(part) > 3:
                            return part

        return None

def test_enhanced_path_extraction():
    """Test the enhanced path extraction with realistic Sublime Text titles."""
    print("Testing Enhanced Path Extraction")
    print("=" * 50)
    
    # Create mock windows with realistic titles from various applications
    test_windows = [
        # Sublime Text examples
        MockWindow("Prompting - C:\\Users\\<USER>\\Desktop\\window_organization.md - Sublime Text",
                  "sublime_text.exe", "Editor"),
        MockWindow("py__WindowTiler - C:\\Users\\<USER>\\Desktop\\my\\flow\\home\\__GOTO__\\Scripts\\Python\\Py_Cli_Utils\\py__WindowTiler - Sublime Text",
                  "sublime_text.exe", "Editor"),
        MockWindow("C:\\Users\\<USER>\\Desktop\\my\\flow\\home\\__GOTO__\\Notes\\system_info.md - Sublime Text",
                  "sublime_text.exe", "Editor"),
        MockWindow("__meta__ - fe wfew ef • - Sublime Text",
                  "sublime_text.exe", "Editor"),

        # VS Code examples
        MockWindow("main.py - my_project - Visual Studio Code",
                  "code.exe", "Editor"),
        MockWindow("C:\\Projects\\webapp\\src\\index.js - Visual Studio Code",
                  "code.exe", "Editor"),

        # Notepad examples
        MockWindow("C:\\Users\\<USER>\\Documents\\notes.txt - Notepad",
                  "notepad.exe", "Editor"),
        MockWindow("config.ini - Notepad",
                  "notepad.exe", "Editor"),

        # Browser examples with file paths
        MockWindow("file:///C:/Users/<USER>/Desktop/test.html - Chrome",
                  "chrome.exe", "Browser"),

        # Generic application with path
        MockWindow("Document1.docx - C:\\Users\\<USER>\\Documents - Microsoft Word",
                  "winword.exe", "Office"),
    ]
    
    print("Mock windows created:")
    for i, window in enumerate(test_windows, 1):
        print(f"{i}. Original: {window.title}")
        print(f"   Extracted Path: {window.detailed_info.get('path', 'None')}")
        print()
    
    # Export to markdown
    print("Exporting to enhanced markdown table...")
    file_path = export_windows_for_editing(test_windows, "Sublime Text Windows")
    print(f"✅ Exported to: {file_path}")
    
    # Show the generated content
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("\nGenerated markdown content:")
    print("-" * 80)
    print(content)
    
    # Clean up
    os.remove(file_path)
    print("✅ Cleaned up temporary file")

if __name__ == "__main__":
    test_enhanced_path_extraction()
