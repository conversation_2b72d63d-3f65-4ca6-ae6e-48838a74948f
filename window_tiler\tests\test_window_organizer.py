#!/usr/bin/env python3
"""
Tests for the enhanced window organizer functionality.
"""

import pytest
import os
import sys
import tempfile
from unittest.mock import Mock, patch

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from window_organizer import WindowExporter, WindowImporter, SimpleWindowOrganizer


class MockWindow:
    """Mock Window class for testing."""
    def __init__(self, title, process_name, window_type="Unknown", path=None):
        self.title = title
        self.process_name = process_name
        self.type = window_type
        self.hwnd = 12345  # Mock HWND
        
        # Mock detailed_info for enhanced functionality
        self.detailed_info = {
            'title': title,
            'path': path,
            'display_name': f"{path} | {title}" if path else title
        }


class TestWindowExporter:
    """Test the enhanced WindowExporter functionality."""
    
    def test_export_with_paths(self):
        """Test that the exporter correctly handles windows with path information."""
        # Create mock windows with path information
        windows = [
            MockWindow("Downloads - File Explorer", "explorer.exe", "Explorer", 
                      "C:\\Users\\<USER>\\Downloads"),
            MockWindow("Documents - File Explorer", "explorer.exe", "Explorer", 
                      "C:\\Users\\<USER>\\Documents"),
            MockWindow("GitHub - Chrome", "chrome.exe", "Browser", None),
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
            exporter = WindowExporter(f.name)
            file_path = exporter.export_windows(windows, "Test Windows")
            
        # Read the generated content
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Verify the enhanced table format
        assert "| Index | Process | Type | Path/Title | Original Title |" in content
        assert "C:\\Users\\<USER>\\Downloads" in content
        assert "C:\\Users\\<USER>\\Documents" in content
        assert "GitHub - Chrome" in content
        
        # Clean up
        os.unlink(file_path)
    
    def test_export_without_paths(self):
        """Test that the exporter handles windows without path information gracefully."""
        windows = [
            MockWindow("Notepad", "notepad.exe", "Editor", None),
            MockWindow("Calculator", "calc.exe", "Other", None),
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
            exporter = WindowExporter(f.name)
            file_path = exporter.export_windows(windows, "Test Windows")
            
        # Read the generated content
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Verify fallback behavior
        assert "Notepad" in content
        assert "Calculator" in content
        
        # Clean up
        os.unlink(file_path)


class TestWindowImporter:
    """Test the WindowImporter functionality."""
    
    def test_import_reordered_windows(self):
        """Test that the importer correctly handles reordered windows."""
        # Create test markdown content with reordered windows
        test_content = """# Window Organization - Test

| Index | Process | Type | Path/Title | Original Title |
|-------|---------|------|------------|----------------|
| 3 | explorer.exe | Explorer | Documents | Documents - File Explorer |
| 1 | explorer.exe | Explorer | Downloads | Downloads - File Explorer |
| 2 | chrome.exe | Browser | GitHub | GitHub - Chrome |
"""
        
        # Create original windows
        original_windows = [
            MockWindow("Downloads - File Explorer", "explorer.exe", "Explorer"),
            MockWindow("GitHub - Chrome", "chrome.exe", "Browser"),
            MockWindow("Documents - File Explorer", "explorer.exe", "Explorer"),
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
            f.write(test_content)
            f.flush()
            
            importer = WindowImporter(f.name)
            reordered = importer.import_window_order(original_windows)
        
        # Verify the reordering (3, 1, 2 -> indices 2, 0, 1)
        assert len(reordered) == 3
        assert reordered[0].title == "Documents - File Explorer"  # Was index 3 (position 2)
        assert reordered[1].title == "Downloads - File Explorer"  # Was index 1 (position 0)
        assert reordered[2].title == "GitHub - Chrome"           # Was index 2 (position 1)
        
        # Clean up
        os.unlink(f.name)


class TestSimpleWindowOrganizer:
    """Test the complete workflow."""
    
    def test_export_import_workflow(self):
        """Test the complete export/import workflow."""
        windows = [
            MockWindow("Window 1", "app1.exe", "App", "C:\\Path1"),
            MockWindow("Window 2", "app2.exe", "App", "C:\\Path2"),
            MockWindow("Window 3", "app3.exe", "App", None),
        ]
        
        with tempfile.TemporaryDirectory() as temp_dir:
            organizer = SimpleWindowOrganizer(temp_dir)
            
            # Export
            with patch.object(organizer, '_open_file_for_editing'):
                file_path = organizer.export_for_editing(windows, "Test Group")
            
            # Verify file was created
            assert os.path.exists(file_path)
            
            # Read and verify content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            assert "C:\\Path1" in content
            assert "C:\\Path2" in content
            assert "Window 3" in content
            
            # Test import (without modification)
            imported = organizer.import_edited_order(windows)
            assert len(imported) == len(windows)


if __name__ == "__main__":
    # Run tests directly
    import traceback

    print("Running Window Organizer Tests...")
    print("=" * 50)

    # Test export functionality
    test_exporter = TestWindowExporter()
    try:
        test_exporter.test_export_with_paths()
        print("✅ Export with paths test passed")
    except Exception as e:
        print(f"❌ Export with paths test failed: {e}")
        traceback.print_exc()

    try:
        test_exporter.test_export_without_paths()
        print("✅ Export without paths test passed")
    except Exception as e:
        print(f"❌ Export without paths test failed: {e}")
        traceback.print_exc()

    # Test import functionality
    test_importer = TestWindowImporter()
    try:
        test_importer.test_import_reordered_windows()
        print("✅ Import reordered windows test passed")
    except Exception as e:
        print(f"❌ Import reordered windows test failed: {e}")
        traceback.print_exc()

    # Test complete workflow
    test_organizer = TestSimpleWindowOrganizer()
    try:
        test_organizer.test_export_import_workflow()
        print("✅ Complete workflow test passed")
    except Exception as e:
        print(f"❌ Complete workflow test failed: {e}")
        traceback.print_exc()

    print("\nAll tests completed!")
